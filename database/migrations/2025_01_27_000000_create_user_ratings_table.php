<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_ratings', function (Blueprint $table) {
            $table->id();
            
            // User và thông tin phân loại
            $table->unsignedBigInteger('user_id')->comment('ID của user được đ<PERSON> gi<PERSON>');
            $table->unsignedBigInteger('shop_id')->nullable()->comment('Chi nhánh');
            $table->unsignedBigInteger('staff_department_id')->nullable()->comment('Phòng ban');
            $table->unsignedBigInteger('team_id')->nullable()->comment('Team');
            $table->unsignedTinyInteger('team_type')->nullable()->comment('Loại team: 1=Traditional, 2=Tiktok');
            
            // Ng<PERSON><PERSON> đ<PERSON><PERSON> gi<PERSON>
            $table->date('rating_date')->comment('<PERSON><PERSON>y đ<PERSON>h giá');
            
            // 4 loại điểm đánh giá (scale 1-10)
            $table->decimal('appointment_score', 4, 2)->nullable()->comment('Điểm lịch hẹn (1-10)');
            $table->decimal('phone_score', 4, 2)->nullable()->comment('Điểm số điện thoại (1-10)');
            $table->decimal('service_quality_score', 4, 2)->nullable()->comment('Điểm chất lượng dịch vụ (1-10)');
            $table->decimal('invoice_score', 4, 2)->nullable()->comment('Điểm đầu hóa đơn (1-10)');
            
            // Tổng điểm và ghi chú
            $table->decimal('total_score', 5, 2)->nullable()->comment('Tổng điểm');
            $table->text('notes')->nullable()->comment('Ghi chú');
            
            // Audit fields
            $table->unsignedBigInteger('created_by')->comment('Người tạo đánh giá');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('Người cập nhật cuối');
            
            $table->timestamps();
            $table->softDeletes();
            
            // Foreign keys
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('shop_id')->references('id')->on('shops')->onDelete('set null');
            $table->foreign('staff_department_id')->references('id')->on('staff_departments')->onDelete('set null');
            $table->foreign('team_id')->references('id')->on('teams')->onDelete('set null');
            $table->foreign('created_by')->references('id')->on('users');
            $table->foreign('updated_by')->references('id')->on('users');
            
            // Indexes
            $table->index(['user_id', 'rating_date']);
            $table->index(['shop_id', 'rating_date']);
            $table->index(['staff_department_id', 'rating_date']);
            $table->index(['team_id', 'rating_date']);
            $table->index(['team_type', 'rating_date']);
            $table->index('rating_date');
            
            // Unique constraint - một user chỉ có một đánh giá mỗi ngày
            $table->unique(['user_id', 'rating_date'], 'user_ratings_user_date_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_ratings');
    }
};
