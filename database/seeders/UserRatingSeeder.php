<?php

namespace Database\Seeders;

use App\Models\UserRating;
use App\Models\User;
use App\Models\Shop;
use App\Models\StaffDepartment;
use App\Models\Team;
use App\Enums\MarketingTeam\TeamTypeEnum;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class UserRatingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get active users
        $users = User::where('active_flag', true)->limit(10)->get();
        $shops = Shop::all();
        $staffDepartments = StaffDepartment::all();
        $teams = Team::all();
        
        if ($users->isEmpty()) {
            $this->command->info('No active users found. Skipping UserRating seeder.');
            return;
        }

        // Create ratings for the last 30 days
        $startDate = Carbon::now()->subDays(30);
        $endDate = Carbon::now();

        $adminUser = User::first(); // Use first user as creator

        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            // Skip weekends for some variety
            if ($date->isWeekend() && rand(0, 1)) {
                continue;
            }

            // Create ratings for random users each day
            $dailyUsers = $users->random(rand(3, 7));

            foreach ($dailyUsers as $user) {
                // Skip if rating already exists for this user and date
                if (UserRating::where('user_id', $user->id)
                    ->where('rating_date', $date->format('Y-m-d'))
                    ->exists()) {
                    continue;
                }

                // Get user's related data
                $shop = $user->team?->shop ?? $shops->random();
                $staffDepartment = $user->staffDepartment ?? $staffDepartments->random();
                $team = $user->team ?? $teams->random();
                
                // Get team type from user's marketing teams or random
                $teamType = null;
                if ($user->marketingTeams()->exists()) {
                    $teamType = $user->marketingTeams()->first()->team_type;
                } else {
                    $teamType = collect([TeamTypeEnum::Traditional, TeamTypeEnum::Tiktok])->random();
                }

                // Generate random scores (with some bias towards higher scores)
                $appointmentScore = $this->generateScore();
                $phoneScore = $this->generateScore();
                $serviceQualityScore = $this->generateScore();
                $invoiceScore = $this->generateScore();

                UserRating::create([
                    'user_id' => $user->id,
                    'shop_id' => $shop?->id,
                    'staff_department_id' => $staffDepartment?->id,
                    'team_id' => $team?->id,
                    'team_type' => $teamType,
                    'rating_date' => $date->format('Y-m-d'),
                    'appointment_score' => $appointmentScore,
                    'phone_score' => $phoneScore,
                    'service_quality_score' => $serviceQualityScore,
                    'invoice_score' => $invoiceScore,
                    'notes' => $this->generateNotes(),
                    'created_by' => $adminUser->id,
                ]);
            }
        }

        $this->command->info('UserRating seeder completed successfully.');
    }

    /**
     * Generate a random score between 1-10 with bias towards higher scores
     */
    private function generateScore(): float
    {
        // 70% chance of score between 6-10, 30% chance of score between 1-5
        if (rand(1, 100) <= 70) {
            return round(rand(60, 100) / 10, 1); // 6.0 - 10.0
        } else {
            return round(rand(10, 50) / 10, 1); // 1.0 - 5.0
        }
    }

    /**
     * Generate random notes
     */
    private function generateNotes(): ?string
    {
        $notes = [
            'Hoàn thành tốt công việc được giao',
            'Cần cải thiện kỹ năng giao tiếp với khách hàng',
            'Rất tích cực trong công việc',
            'Đạt chỉ tiêu đề ra',
            'Cần hỗ trợ thêm về kỹ năng bán hàng',
            'Xuất sắc trong việc chăm sóc khách hàng',
            'Thái độ làm việc tích cực',
            'Cần cải thiện tốc độ xử lý công việc',
            null, // Some ratings without notes
            null,
        ];

        return collect($notes)->random();
    }
}
