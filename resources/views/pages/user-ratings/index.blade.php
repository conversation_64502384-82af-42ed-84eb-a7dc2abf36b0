@extends('layouts.app')

@section('title', 'Đánh giá xếp hạng nhân viên')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Đánh giá xếp hạng nhân viên</h3>
                    @can('create', App\Models\UserRating::class)
                        <a href="{{ route('user-ratings.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Thêm đánh giá
                        </a>
                    @endcan
                </div>

                <!-- Filter Form -->
                <div class="card-body">
                    <form method="GET" action="{{ route('user-ratings.index') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="user_id" class="form-label">Nhân viên</label>
                                <select name="user_id" id="user_id" class="form-select">
                                    <option value="">-- Tất cả nhân viên --</option>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}" {{ $request->user_id == $user->id ? 'selected' : '' }}>
                                            {{ $user->name }} ({{ $user->code }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="col-md-3">
                                <label for="shop_id" class="form-label">Chi nhánh</label>
                                <select name="shop_id" id="shop_id" class="form-select">
                                    <option value="">-- Tất cả chi nhánh --</option>
                                    @foreach($shops as $shop)
                                        <option value="{{ $shop->id }}" {{ $request->shop_id == $shop->id ? 'selected' : '' }}>
                                            {{ $shop->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="col-md-3">
                                <label for="staff_department_id" class="form-label">Phòng ban</label>
                                <select name="staff_department_id" id="staff_department_id" class="form-select">
                                    <option value="">-- Tất cả phòng ban --</option>
                                    @foreach($staffDepartments as $dept)
                                        <option value="{{ $dept->id }}" {{ $request->staff_department_id == $dept->id ? 'selected' : '' }}>
                                            {{ $dept->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="col-md-3">
                                <label for="team_type" class="form-label">Loại team</label>
                                <select name="team_type" id="team_type" class="form-select">
                                    <option value="">-- Tất cả loại team --</option>
                                    @foreach($teamTypeOptions as $value => $label)
                                        <option value="{{ $value }}" {{ $request->team_type == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-3">
                                <label for="date_from" class="form-label">Từ ngày</label>
                                <input type="date" name="date_from" id="date_from" class="form-control" 
                                       value="{{ $request->date_from }}">
                            </div>

                            <div class="col-md-3">
                                <label for="date_to" class="form-label">Đến ngày</label>
                                <input type="date" name="date_to" id="date_to" class="form-control" 
                                       value="{{ $request->date_to }}">
                            </div>

                            <div class="col-md-4">
                                <label for="search" class="form-label">Tìm kiếm</label>
                                <input type="text" name="search" id="search" class="form-control" 
                                       placeholder="Tên, mã nhân viên..." value="{{ $request->search }}">
                            </div>

                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Lọc
                                </button>
                                <a href="{{ route('user-ratings.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-undo"></i> Reset
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Results Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>STT</th>
                                    <th>Nhân viên</th>
                                    <th>Chi nhánh</th>
                                    <th>Phòng ban</th>
                                    <th>Team</th>
                                    <th>Loại team</th>
                                    <th>Ngày đánh giá</th>
                                    <th>Lịch hẹn</th>
                                    <th>Số ĐT</th>
                                    <th>Chất lượng DV</th>
                                    <th>Đầu HĐ</th>
                                    <th class="text-center">Tổng điểm</th>
                                    <th>Người tạo</th>
                                    <th class="text-center">Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($userRatings as $index => $rating)
                                    <tr>
                                        <td>{{ $userRatings->firstItem() + $index }}</td>
                                        <td>
                                            <strong>{{ $rating->user->name }}</strong><br>
                                            <small class="text-muted">{{ $rating->user->code }}</small>
                                        </td>
                                        <td>{{ $rating->shop->name ?? '-' }}</td>
                                        <td>{{ $rating->staffDepartment->name ?? '-' }}</td>
                                        <td>{{ $rating->team->name ?? '-' }}</td>
                                        <td>
                                            @if($rating->team_type)
                                                <span class="badge bg-{{ $rating->team_type == 1 ? 'primary' : 'success' }}">
                                                    {{ $rating->team_type_name }}
                                                </span>
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td>{{ $rating->rating_date->format('d/m/Y') }}</td>
                                        <td class="text-center">
                                            @if($rating->appointment_score)
                                                <span class="badge bg-info">{{ number_format($rating->appointment_score, 1) }}</span>
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            @if($rating->phone_score)
                                                <span class="badge bg-info">{{ number_format($rating->phone_score, 1) }}</span>
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            @if($rating->service_quality_score)
                                                <span class="badge bg-info">{{ number_format($rating->service_quality_score, 1) }}</span>
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            @if($rating->invoice_score)
                                                <span class="badge bg-info">{{ number_format($rating->invoice_score, 1) }}</span>
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-warning text-dark fs-6">
                                                {{ number_format($rating->total_score, 1) }}
                                            </span>
                                        </td>
                                        <td>{{ $rating->creator->name ?? '-' }}</td>
                                        <td class="text-center">
                                            @can('update', $rating)
                                                <a href="{{ route('user-ratings.edit', $rating) }}" 
                                                   class="btn btn-sm btn-warning" title="Sửa">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            @endcan
                                            
                                            @can('delete', $rating)
                                                <form method="POST" action="{{ route('user-ratings.destroy', $rating) }}" 
                                                      class="d-inline" onsubmit="return confirm('Bạn có chắc chắn muốn xóa?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger" title="Xóa">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            @endcan
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="14" class="text-center">Không có dữ liệu</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $userRatings->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize Select2 for better UX
    $('#user_id, #shop_id, #staff_department_id, #team_type').select2({
        theme: 'bootstrap-5',
        width: '100%'
    });
});
</script>
@endpush
