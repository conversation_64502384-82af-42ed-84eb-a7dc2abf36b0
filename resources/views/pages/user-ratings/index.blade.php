@extends('layouts.authenticated')

@section('page.title', 'Đánh giá xếp hạng nhân viên')

@section('page.breadcrumb')
    <ol class="breadcrumb">
        <li><a href="{{ route('auth.home') }}"><i class="fa fa-dashboard"></i> Trang chủ</a></li>
        <li class="active">Đánh giá xếp hạng nhân viên</li>
    </ol>
@endsection

@section('page.content')
<div class="box">
    <div class="box-header with-border">
        <div class="object-list-action text-right">
            @can('create', App\Models\UserRating::class)
                @include('includes.button.create', [
                    'url' => route('user-ratings.create'),
                ])
            @endcan
        </div>
    </div>

    <div class="box-body">
        <div class="object-list-filter">
            <form method="GET" action="{{ route('user-ratings.index') }}" id="filter-form">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Nhân viên</label>
                            <select name="user_id" class="form-control select2" data-placeholder="Tất cả nhân viên">
                                <option value="">Tất cả nhân viên</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}" {{ $request->user_id == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }} ({{ $user->code }})
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Chi nhánh</label>
                            <select name="shop_id" class="form-control select2" data-placeholder="Tất cả chi nhánh">
                                <option value="">Tất cả chi nhánh</option>
                                @foreach($shops as $shop)
                                    <option value="{{ $shop->id }}" {{ $request->shop_id == $shop->id ? 'selected' : '' }}>
                                        {{ $shop->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Phòng ban</label>
                            <select name="staff_department_id" class="form-control select2" data-placeholder="Tất cả phòng ban">
                                <option value="">Tất cả phòng ban</option>
                                @foreach($staffDepartments as $dept)
                                    <option value="{{ $dept->id }}" {{ $request->staff_department_id == $dept->id ? 'selected' : '' }}>
                                        {{ $dept->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Loại team</label>
                            <select name="team_type" class="form-control select2" data-placeholder="Tất cả loại team">
                                <option value="">Tất cả loại team</option>
                                @foreach($teamTypeOptions as $value => $label)
                                    <option value="{{ $value }}" {{ $request->team_type == $value ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Từ ngày</label>
                            <input type="date" name="date_from" class="form-control" value="{{ $request->date_from }}">
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Đến ngày</label>
                            <input type="date" name="date_to" class="form-control" value="{{ $request->date_to }}">
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Tìm kiếm</label>
                            <input type="text" name="search" class="form-control"
                                   placeholder="Tên, mã nhân viên..." value="{{ $request->search }}">
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-search"></i> Lọc
                                </button>
                                <a href="{{ route('user-ratings.index') }}" class="btn btn-default">
                                    <i class="fa fa-refresh"></i> Reset
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead>
                                <tr>
                                    <th>STT</th>
                                    <th>Nhân viên</th>
                                    <th>Chi nhánh</th>
                                    <th>Phòng ban</th>
                                    <th>Team</th>
                                    <th>Loại team</th>
                                    <th>Ngày đánh giá</th>
                                    <th>Lịch hẹn</th>
                                    <th>Số ĐT</th>
                                    <th>Chất lượng DV</th>
                                    <th>Đầu HĐ</th>
                                    <th class="text-center">Tổng điểm</th>
                                    <th>Người tạo</th>
                                    <th class="text-center">Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($userRatings as $index => $rating)
                                    <tr>
                                        <td>{{ $userRatings->firstItem() + $index }}</td>
                                        <td>
                                            <strong>{{ $rating->user->name }}</strong><br>
                                            <small class="text-muted">{{ $rating->user->code }}</small>
                                        </td>
                                        <td>{{ $rating->shop->name ?? '-' }}</td>
                                        <td>{{ $rating->staffDepartment->name ?? '-' }}</td>
                                        <td>{{ $rating->team->name ?? '-' }}</td>
                                        <td>
                                            @if($rating->team_type)
                                                <span class="badge bg-{{ $rating->team_type == 1 ? 'primary' : 'success' }}">
                                                    {{ $rating->team_type_name }}
                                                </span>
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td>{{ $rating->rating_date->format('d/m/Y') }}</td>
                                        <td class="text-center">
                                            @if($rating->appointment_score)
                                                <span class="badge bg-info">{{ number_format($rating->appointment_score, 1) }}</span>
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            @if($rating->phone_score)
                                                <span class="badge bg-info">{{ number_format($rating->phone_score, 1) }}</span>
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            @if($rating->service_quality_score)
                                                <span class="badge bg-info">{{ number_format($rating->service_quality_score, 1) }}</span>
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            @if($rating->invoice_score)
                                                <span class="badge bg-info">{{ number_format($rating->invoice_score, 1) }}</span>
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-warning text-dark fs-6">
                                                {{ number_format($rating->total_score, 1) }}
                                            </span>
                                        </td>
                                        <td>{{ $rating->creator->name ?? '-' }}</td>
                                        <td class="text-center">
                                            @can('update', $rating)
                                                @include('includes.button.edit', [
                                                    'url' => route('user-ratings.edit', $rating),
                                                ])
                                            @endcan

                                            @can('delete', $rating)
                                                @include('includes.button.delete', [
                                                    'url' => route('user-ratings.destroy', $rating),
                                                    'confirm_message' => 'Bạn có chắc chắn muốn xóa đánh giá này?'
                                                ])
                                            @endcan
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="14" class="text-center">Không có dữ liệu</td>
                                    </tr>
                                @endforelse
                </tbody>
            </table>
        </div>

        <div class="text-right">
            {{ $userRatings->appends(request()->query())->links() }}
        </div>
    </div>
</div>

@include('includes.form.delete')
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        width: '100%',
        allowClear: true
    });
});
</script>
@endpush
