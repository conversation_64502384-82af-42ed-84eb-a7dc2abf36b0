@extends('layouts.app')

@section('title', $isEdit ? 'Sửa đánh giá nhân viên' : 'Thêm đánh giá nhân viên')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {{ $isEdit ? 'Sửa đánh giá nhân viên' : 'Thêm đánh giá nhân viên' }}
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('user-ratings.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                    </div>
                </div>

                <form method="POST" action="{{ $isEdit ? route('user-ratings.update', $userRating) : route('user-ratings.store') }}">
                    @csrf
                    @if($isEdit)
                        @method('PUT')
                    @endif

                    <div class="card-body">
                        <div class="row">
                            <!-- User Selection -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="user_id" class="form-label">Nhân viên <span class="text-danger">*</span></label>
                                    <select name="user_id" id="user_id" class="form-select @error('user_id') is-invalid @enderror" required>
                                        <option value="">-- Chọn nhân viên --</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->id }}" 
                                                {{ old('user_id', $isEdit ? $userRating->user_id : '') == $user->id ? 'selected' : '' }}>
                                                {{ $user->name }} ({{ $user->code }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('user_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Rating Date -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="rating_date" class="form-label">Ngày đánh giá <span class="text-danger">*</span></label>
                                    <input type="date" name="rating_date" id="rating_date" 
                                           class="form-control @error('rating_date') is-invalid @enderror"
                                           value="{{ old('rating_date', $isEdit ? $userRating->rating_date->format('Y-m-d') : date('Y-m-d')) }}" required>
                                    @error('rating_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Shop -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="shop_id" class="form-label">Chi nhánh</label>
                                    <select name="shop_id" id="shop_id" class="form-select @error('shop_id') is-invalid @enderror">
                                        <option value="">-- Chọn chi nhánh --</option>
                                        @foreach($shops as $shop)
                                            <option value="{{ $shop->id }}" 
                                                {{ old('shop_id', $isEdit ? $userRating->shop_id : '') == $shop->id ? 'selected' : '' }}>
                                                {{ $shop->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('shop_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Staff Department -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="staff_department_id" class="form-label">Phòng ban</label>
                                    <select name="staff_department_id" id="staff_department_id" class="form-select @error('staff_department_id') is-invalid @enderror">
                                        <option value="">-- Chọn phòng ban --</option>
                                        @foreach($staffDepartments as $dept)
                                            <option value="{{ $dept->id }}" 
                                                {{ old('staff_department_id', $isEdit ? $userRating->staff_department_id : '') == $dept->id ? 'selected' : '' }}>
                                                {{ $dept->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('staff_department_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Team -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="team_id" class="form-label">Team</label>
                                    <select name="team_id" id="team_id" class="form-select @error('team_id') is-invalid @enderror">
                                        <option value="">-- Chọn team --</option>
                                        @foreach($teams as $team)
                                            <option value="{{ $team->id }}" 
                                                {{ old('team_id', $isEdit ? $userRating->team_id : '') == $team->id ? 'selected' : '' }}>
                                                {{ $team->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('team_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Team Type -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="team_type" class="form-label">Loại team</label>
                                    <select name="team_type" id="team_type" class="form-select @error('team_type') is-invalid @enderror">
                                        <option value="">-- Chọn loại team --</option>
                                        @foreach($teamTypeOptions as $value => $label)
                                            <option value="{{ $value }}" 
                                                {{ old('team_type', $isEdit ? $userRating->team_type : '') == $value ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('team_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Rating Scores -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3">Điểm đánh giá (thang điểm 1-10)</h5>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Appointment Score -->
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="appointment_score" class="form-label">Điểm lịch hẹn</label>
                                    <input type="number" name="appointment_score" id="appointment_score" 
                                           class="form-control @error('appointment_score') is-invalid @enderror"
                                           min="0" max="10" step="0.1"
                                           value="{{ old('appointment_score', $isEdit ? $userRating->appointment_score : '') }}">
                                    @error('appointment_score')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Phone Score -->
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="phone_score" class="form-label">Điểm số điện thoại</label>
                                    <input type="number" name="phone_score" id="phone_score" 
                                           class="form-control @error('phone_score') is-invalid @enderror"
                                           min="0" max="10" step="0.1"
                                           value="{{ old('phone_score', $isEdit ? $userRating->phone_score : '') }}">
                                    @error('phone_score')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Service Quality Score -->
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="service_quality_score" class="form-label">Điểm chất lượng dịch vụ</label>
                                    <input type="number" name="service_quality_score" id="service_quality_score" 
                                           class="form-control @error('service_quality_score') is-invalid @enderror"
                                           min="0" max="10" step="0.1"
                                           value="{{ old('service_quality_score', $isEdit ? $userRating->service_quality_score : '') }}">
                                    @error('service_quality_score')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Invoice Score -->
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="invoice_score" class="form-label">Điểm đầu hóa đơn</label>
                                    <input type="number" name="invoice_score" id="invoice_score" 
                                           class="form-control @error('invoice_score') is-invalid @enderror"
                                           min="0" max="10" step="0.1"
                                           value="{{ old('invoice_score', $isEdit ? $userRating->invoice_score : '') }}">
                                    @error('invoice_score')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">Ghi chú</label>
                                    <textarea name="notes" id="notes" rows="3" 
                                              class="form-control @error('notes') is-invalid @enderror"
                                              placeholder="Nhập ghi chú (tùy chọn)">{{ old('notes', $isEdit ? $userRating->notes : '') }}</textarea>
                                    @error('notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Total Score Display -->
                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <strong>Tổng điểm:</strong> <span id="total_score_display">0</span>
                                    <small class="text-muted">(Tự động tính toán dựa trên các điểm đã nhập)</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> {{ $isEdit ? 'Cập nhật' : 'Lưu' }}
                        </button>
                        <a href="{{ route('user-ratings.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Hủy
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize Select2
    $('#user_id, #shop_id, #staff_department_id, #team_id, #team_type').select2({
        theme: 'bootstrap-5',
        width: '100%'
    });

    // Auto-fill user info when user is selected
    $('#user_id').on('change', function() {
        const userId = $(this).val();
        if (userId) {
            $.get(`/user-ratings/get-user-info/${userId}`)
                .done(function(data) {
                    if (data.shop_id) $('#shop_id').val(data.shop_id).trigger('change');
                    if (data.staff_department_id) $('#staff_department_id').val(data.staff_department_id).trigger('change');
                    if (data.team_id) $('#team_id').val(data.team_id).trigger('change');
                    if (data.team_type) $('#team_type').val(data.team_type).trigger('change');
                })
                .fail(function() {
                    console.log('Failed to fetch user info');
                });
        }
    });

    // Calculate total score
    function calculateTotalScore() {
        let total = 0;
        const scores = [
            parseFloat($('#appointment_score').val()) || 0,
            parseFloat($('#phone_score').val()) || 0,
            parseFloat($('#service_quality_score').val()) || 0,
            parseFloat($('#invoice_score').val()) || 0
        ];
        
        total = scores.reduce((sum, score) => sum + score, 0);
        $('#total_score_display').text(total.toFixed(1));
    }

    // Update total score when any score changes
    $('#appointment_score, #phone_score, #service_quality_score, #invoice_score').on('input', calculateTotalScore);

    // Calculate initial total score
    calculateTotalScore();
});
</script>
@endpush
