@extends('layouts.app')

@section('title', 'Báo cáo xếp hạng nhân viên')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Báo cáo xếp hạng nhân viên</h3>
                    <div class="card-tools">
                        @can('exportReports', App\Models\UserRating::class)
                            <button type="button" class="btn btn-success" onclick="exportReport()">
                                <i class="fas fa-file-excel"></i> Xuất Excel
                            </button>
                        @endcan
                    </div>
                </div>

                <!-- Filter Form -->
                <div class="card-body">
                    <form method="GET" action="{{ route('user-rating-reports.index') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="report_type" class="form-label">Loại báo cáo</label>
                                <select name="report_type" id="report_type" class="form-select">
                                    <option value="daily" {{ $reportType == 'daily' ? 'selected' : '' }}>Theo ngày</option>
                                    <option value="weekly" {{ $reportType == 'weekly' ? 'selected' : '' }}>Theo tuần</option>
                                    <option value="monthly" {{ $reportType == 'monthly' ? 'selected' : '' }}>Theo tháng</option>
                                </select>
                            </div>

                            <div class="col-md-3">
                                <label for="group_by" class="form-label">Nhóm theo</label>
                                <select name="group_by" id="group_by" class="form-select">
                                    <option value="user" {{ $groupBy == 'user' ? 'selected' : '' }}>Nhân viên</option>
                                    <option value="shop" {{ $groupBy == 'shop' ? 'selected' : '' }}>Chi nhánh</option>
                                    <option value="staff_department" {{ $groupBy == 'staff_department' ? 'selected' : '' }}>Phòng ban</option>
                                    <option value="team" {{ $groupBy == 'team' ? 'selected' : '' }}>Team</option>
                                    <option value="team_type" {{ $groupBy == 'team_type' ? 'selected' : '' }}>Loại team</option>
                                </select>
                            </div>

                            <div class="col-md-3">
                                <label for="date_from" class="form-label">Từ ngày</label>
                                <input type="date" name="date_from" id="date_from" class="form-control" 
                                       value="{{ $dateFrom }}">
                            </div>

                            <div class="col-md-3">
                                <label for="date_to" class="form-label">Đến ngày</label>
                                <input type="date" name="date_to" id="date_to" class="form-control" 
                                       value="{{ $dateTo }}">
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-3">
                                <label for="shop_id" class="form-label">Chi nhánh</label>
                                <select name="shop_id" id="shop_id" class="form-select">
                                    <option value="">-- Tất cả chi nhánh --</option>
                                    @foreach($shops as $shop)
                                        <option value="{{ $shop->id }}" {{ $request->shop_id == $shop->id ? 'selected' : '' }}>
                                            {{ $shop->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="col-md-3">
                                <label for="staff_department_id" class="form-label">Phòng ban</label>
                                <select name="staff_department_id" id="staff_department_id" class="form-select">
                                    <option value="">-- Tất cả phòng ban --</option>
                                    @foreach($staffDepartments as $dept)
                                        <option value="{{ $dept->id }}" {{ $request->staff_department_id == $dept->id ? 'selected' : '' }}>
                                            {{ $dept->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="col-md-3">
                                <label for="team_type" class="form-label">Loại team</label>
                                <select name="team_type" id="team_type" class="form-select">
                                    <option value="">-- Tất cả loại team --</option>
                                    @foreach($teamTypeOptions as $value => $label)
                                        <option value="{{ $value }}" {{ $request->team_type == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-chart-bar"></i> Tạo báo cáo
                                </button>
                                <a href="{{ route('user-rating-reports.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-undo"></i> Reset
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Report Results -->
                    @if($reportData->isNotEmpty())
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>STT</th>
                                        @if($reportType == 'daily')
                                            <th>Ngày</th>
                                        @elseif($reportType == 'weekly')
                                            <th>Tuần</th>
                                        @elseif($reportType == 'monthly')
                                            <th>Tháng</th>
                                        @endif

                                        @if($groupBy == 'user')
                                            <th>Nhân viên</th>
                                        @elseif($groupBy == 'shop')
                                            <th>Chi nhánh</th>
                                        @elseif($groupBy == 'staff_department')
                                            <th>Phòng ban</th>
                                        @elseif($groupBy == 'team')
                                            <th>Team</th>
                                        @elseif($groupBy == 'team_type')
                                            <th>Loại team</th>
                                        @endif

                                        <th class="text-center">Số lần đánh giá</th>
                                        <th class="text-center">TB Lịch hẹn</th>
                                        <th class="text-center">TB Số ĐT</th>
                                        <th class="text-center">TB Chất lượng DV</th>
                                        <th class="text-center">TB Đầu HĐ</th>
                                        <th class="text-center">TB Tổng điểm</th>
                                        <th class="text-center">Tổng điểm</th>
                                        <th class="text-center">Điểm cao nhất</th>
                                        <th class="text-center">Điểm thấp nhất</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($reportData as $index => $row)
                                        <tr>
                                            <td>{{ $index + 1 }}</td>
                                            
                                            <!-- Time Period -->
                                            @if($reportType == 'daily')
                                                <td>{{ \Carbon\Carbon::parse($row['rating_date'])->format('d/m/Y') }}</td>
                                            @elseif($reportType == 'weekly')
                                                <td>
                                                    Tuần {{ $row['week'] }}/{{ $row['year'] }}<br>
                                                    <small class="text-muted">
                                                        {{ \Carbon\Carbon::parse($row['week_start'])->format('d/m') }} - 
                                                        {{ \Carbon\Carbon::parse($row['week_end'])->format('d/m') }}
                                                    </small>
                                                </td>
                                            @elseif($reportType == 'monthly')
                                                <td>{{ $row['month'] }}/{{ $row['year'] }}</td>
                                            @endif

                                            <!-- Group By Field -->
                                            @if($groupBy == 'user')
                                                <td>
                                                    <strong>{{ $row['user_name'] }}</strong><br>
                                                    <small class="text-muted">{{ $row['user_code'] }}</small>
                                                </td>
                                            @elseif($groupBy == 'shop')
                                                <td>{{ $row['shop_name'] }}</td>
                                            @elseif($groupBy == 'staff_department')
                                                <td>{{ $row['staff_department_name'] }}</td>
                                            @elseif($groupBy == 'team')
                                                <td>{{ $row['team_name'] }}</td>
                                            @elseif($groupBy == 'team_type')
                                                <td>
                                                    <span class="badge bg-{{ $row['team_type'] == 1 ? 'primary' : 'success' }}">
                                                        {{ $row['team_type_name'] }}
                                                    </span>
                                                </td>
                                            @endif

                                            <td class="text-center">{{ $row['total_ratings'] }}</td>
                                            <td class="text-center">{{ $row['avg_appointment_score'] ?: '-' }}</td>
                                            <td class="text-center">{{ $row['avg_phone_score'] ?: '-' }}</td>
                                            <td class="text-center">{{ $row['avg_service_quality_score'] ?: '-' }}</td>
                                            <td class="text-center">{{ $row['avg_invoice_score'] ?: '-' }}</td>
                                            <td class="text-center">
                                                <span class="badge bg-warning text-dark">
                                                    {{ $row['avg_total_score'] }}
                                                </span>
                                            </td>
                                            <td class="text-center">{{ number_format($row['sum_total_score'], 1) }}</td>
                                            <td class="text-center">{{ number_format($row['max_total_score'], 1) }}</td>
                                            <td class="text-center">{{ number_format($row['min_total_score'], 1) }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i> Không có dữ liệu cho khoảng thời gian đã chọn.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Top Performers Widget -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Top 10 nhân viên xuất sắc</h3>
                </div>
                <div class="card-body">
                    <div id="top-performers-container">
                        <div class="text-center">
                            <i class="fas fa-spinner fa-spin"></i> Đang tải...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize Select2
    $('#report_type, #group_by, #shop_id, #staff_department_id, #team_type').select2({
        theme: 'bootstrap-5',
        width: '100%'
    });

    // Load top performers
    loadTopPerformers();

    function loadTopPerformers() {
        const dateFrom = $('#date_from').val();
        const dateTo = $('#date_to').val();
        
        $.get('{{ route("user-rating-reports.top-performers") }}', {
            date_from: dateFrom,
            date_to: dateTo,
            limit: 10
        })
        .done(function(data) {
            let html = '<div class="row">';
            
            if (data.length > 0) {
                data.forEach(function(performer, index) {
                    const badgeClass = index === 0 ? 'bg-warning' : index === 1 ? 'bg-secondary' : index === 2 ? 'bg-info' : 'bg-light text-dark';
                    const icon = index === 0 ? 'fa-trophy' : index === 1 ? 'fa-medal' : index === 2 ? 'fa-award' : 'fa-star';
                    
                    html += `
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas ${icon} fa-2x text-warning mb-2"></i>
                                    <h6 class="card-title">${performer.user_name}</h6>
                                    <p class="card-text">
                                        <small class="text-muted">${performer.user_code}</small><br>
                                        <span class="badge ${badgeClass} fs-6">${parseFloat(performer.avg_total_score).toFixed(1)}</span>
                                    </p>
                                    <small class="text-muted">${performer.total_ratings} lần đánh giá</small>
                                </div>
                            </div>
                        </div>
                    `;
                });
            } else {
                html += '<div class="col-12 text-center">Không có dữ liệu</div>';
            }
            
            html += '</div>';
            $('#top-performers-container').html(html);
        })
        .fail(function() {
            $('#top-performers-container').html('<div class="alert alert-danger">Không thể tải dữ liệu top performers</div>');
        });
    }
});

function exportReport() {
    const form = $('form').first();
    const params = form.serialize();
    window.open(`{{ route('user-rating-reports.export') }}?${params}`, '_blank');
}
</script>
@endpush
