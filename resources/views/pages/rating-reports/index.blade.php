@extends('layouts.authenticated')

@section('page.title', 'Báo cáo đ<PERSON>h giá bác sĩ theo dịch vụ')

@section('page.css')
    <link rel="stylesheet" href="{{ asset('css/rating-custom.css') }}?v={{ time() }}">
    <link rel="stylesheet" href="{{ asset('css/app.css') }}?v={{ time() }}">
    <meta name="locale" content="vi-VN">
@endsection

@section('page.breadcrumb')
    <ol class="breadcrumb">
        <li><a href="{{ route('auth.home') }}"><i class="fa fa-dashboard"></i> Trang chủ</a></li>
        <li class="active">Báo cáo đánh giá</li>
    </ol>
@endsection

@section('page.content')
    <div class="row">
        <div class="col-md-12">
            <!-- Filter Box -->
            <div class="box">
                <div class="box-body">
                    <div class="object-list-filter">
                        <form method="GET" action="{{ route('rating-reports.index') }}" id="filter-form">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="filter-section">
                                        <h4><i class="fa fa-filter"></i> Bộ lọc</h4>
                                        <button type="button" class="btn btn-xs btn-default pull-right" id="toggle-filter">
                                            <i class="fa fa-chevron-down"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="filter-content" id="filter-content">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>Chi nhánh</label>
                                            <select name="shop_id" class="form-control select2" data-placeholder="Tất cả">
                                                <option value="">Tất cả</option>
                                                @foreach($shops as $shop)
                                                    <option value="{{ $shop->id }}" {{ $filters['shop_id'] == $shop->id ? 'selected' : '' }}>
                                                        {{ $shop->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label>Tháng</label>
                                            <select name="month_only" class="form-control select2" data-placeholder="Tháng">
                                                @php
                                                    // Nếu có filter month thì dùng, không thì dùng tháng hiện tại làm mặc định
                                                    $filterMonth = $filters['month'] ?? date('Y-m');
                                                    $currentMonthParts = explode('-', $filterMonth);
                                                    $currentMonthOnly = $currentMonthParts[1] ?? '';
                                                @endphp
                                                <option value="">Tất cả</option>
                                                @for($m = 1; $m <= 12; $m++)
                                                    @php $monthValue = str_pad($m, 2, '0', STR_PAD_LEFT); @endphp
                                                    <option value="{{ $monthValue }}" {{ $currentMonthOnly == $monthValue ? 'selected' : '' }}>
                                                        Tháng {{ $m }}
                                                    </option>
                                                @endfor
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label>Năm</label>
                                            <select name="year" class="form-control select2" data-placeholder="Năm">
                                                @php
                                                    $currentYear = $currentMonthParts[0] ?? date('Y');
                                                    $startYear = date('Y') - 2;
                                                    $endYear = date('Y') + 2;
                                                @endphp
                                                <option value="">Tất cả</option>
                                                @for($y = $startYear; $y <= $endYear; $y++)
                                                    <option value="{{ $y }}" {{ $currentYear == $y ? 'selected' : '' }}>
                                                        {{ $y }}
                                                    </option>
                                                @endfor
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>Điểm tối thiểu</label>
                                            <input type="number" name="min_rating" class="form-control" min="0" max="10" step="0.1"
                                                   value="{{ $filters['min_rating'] }}" placeholder="0-10">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12 filter-buttons">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fa fa-search"></i> Lọc dữ liệu
                                        </button>
                                        <a href="{{ route('rating-reports.index') }}" class="btn btn-default">
                                            <i class="fa fa-refresh"></i> Đặt lại
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>



            <!-- Matrix Report -->
            <div class="box">
                <div class="box-header with-border">
                    <h3 class="box-title">
                        <i class="fa fa-table"></i> Ma trận đánh giá bác sĩ theo dịch vụ
                        @if(!empty($filters['month']))
                            - Tháng {{ \Carbon\Carbon::parse($filters['month'] . '-01')->format('m/Y') }}
                        @endif
                    </h3>
                </div>
                <div class="box-body no-padding">
                    @if ($reportData['doctors']->isNotEmpty())
                        <div class="table-responsive table-scroll table-lead-report">
                            <table class="table table-bordered table-custom">
                                <thead>
                                    <tr class="table-tr-name">
                                        <th class="sticky-col-lead-report-1" style="text-align: start; min-width: 150px; background-color: lightseagreen !important; color: #fff !important;">
                                            <strong>Bác sĩ / Dịch vụ</strong>
                                        </th>
                                        @foreach($reportData['services'] as $serviceKey => $serviceName)
                                            <th style="min-width: 100px; background-color: lightseagreen !important; color: #fff !important; text-align: center; padding: 10px;">
                                                <div style="font-size: 13px; line-height: 1.2; color: #fff;">{{ $serviceName }}</div>
                                            </th>
                                        @endforeach
                                    </tr>
                                </thead>
                                <tbody class="table-body-center">
                                    @foreach($reportData['doctors'] as $doctor)
                                        <tr>
                                            <td class="sticky-col-lead-report-1" style="text-align: start; font-weight: 500;">
                                                {{ $doctor->name }}
                                            </td>
                                            @foreach($reportData['services'] as $serviceKey => $serviceName)
                                                @php
                                                    $data = $reportData['matrix'][$doctor->id][$serviceKey] ?? null;
                                                @endphp
                                                <td style="text-align: center; vertical-align: middle;">
                                                    @if($data && $data['average'] !== null)
                                                        <div class="rating-simple">
                                                            <div class="rating-score {{ $data['average'] >= 8 ? 'text-success' : ($data['average'] >= 6 ? 'text-warning' : 'text-danger') }}">
                                                                <strong>{{ $data['average'] }}</strong>
                                                            </div>
                                                            <small class="text-muted">Số lượng đánh giá: {{ $data['count'] }}</small>
                                                        </div>
                                                    @else
                                                        <span class="text-muted">--</span>
                                                    @endif
                                                </td>
                                            @endforeach
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="box-body">
                            <p class="text-center text-muted">Không có dữ liệu để hiển thị.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
/* Lead report table styles for rating matrix */
@media print {
    th {
        min-width: 20px !important;
        border: 1px solid #000000;
    }

    td {
        min-width: 20px !important;
        border: 1px solid #000000;
    }
}

/* Simple rating display */
.rating-simple {
    padding: 5px;
}

.rating-simple .rating-score {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 2px;
}

.rating-simple small {
    font-size: 11px;
    display: block;
}

/* Sticky column styles for rating matrix */
.sticky-col-lead-report-1 {
    position: sticky;
    left: 0;
    background-color: lightseagreen !important;
    z-index: 10;
    border-right: 2px solid #ddd;
    color: #fff !important;
}

/* Header styling for rating matrix */
.table-lead-report .table-tr-name th {
    background-color: lightseagreen !important;
    color: #fff !important;
    font-weight: bold;
    text-align: center;
    vertical-align: middle !important;
    position: sticky;
    top: 0;
    z-index: 9;
}

/* Ensure all header text is white */
.table-lead-report .table-tr-name th * {
    color: #fff !important;
}

.table-lead-report .table-tr-name th small {
    color: #e0e0e0 !important;
}

/* Override sticky column header to have higher z-index */
.table-lead-report .sticky-col-lead-report-1 {
    z-index: 11;
}

/* Table improvements */
.table-lead-report table {
    font-size: 14px;
}

.table-lead-report td {
    padding: 12px 8px;
    border: 1px solid #e0e0e0;
}

.table-lead-report th {
    padding: 12px 8px;
    border: 1px solid #ddd;
    background-color: lightseagreen !important;
    color: #fff !important;
}

/* Row hover effect */
.table-lead-report tbody tr:hover {
    background-color: #f8f9fa;
}

/* Alternating row colors */
.table-lead-report tbody tr:nth-child(even) {
    background-color: #fafafa;
}

.table-lead-report tbody tr:nth-child(even):hover {
    background-color: #f0f0f0;
}

/* Force header background color - highest priority */
.table-lead-report thead th,
.table-lead-report .table-tr-name th,
th.sticky-col-lead-report-1 {
    background-color: lightseagreen !important;
    color: #fff !important;
}

.table-lead-report thead th *,
.table-lead-report .table-tr-name th *,
th.sticky-col-lead-report-1 * {
    color: #fff !important;
}

.table-lead-report thead th small,
.table-lead-report .table-tr-name th small,
th.sticky-col-lead-report-1 small {
    color: #e0e0e0 !important;
}


</style>
@endpush

@push('scripts')
<script>
// Set document locale to Vietnamese
document.documentElement.lang = 'vi-VN';

$(function() {
    // Initialize Select2
    $('.select2').select2({
        width: '100%',
        allowClear: true,
        language: {
            noResults: function() {
                return "Không tìm thấy kết quả";
            },
            searching: function() {
                return "Đang tìm...";
            }
        }
    });

    // Toggle filter content
    $('#toggle-filter').on('click', function(e) {
        e.preventDefault();
        $('#filter-content').slideToggle('fast');
        $(this).find('i').toggleClass('fa-chevron-down fa-chevron-up');
    });

    // Check if any filter is applied
    var hasFilter = {{ count(request()->except(['page'])) > 0 ? 'true' : 'false' }};

    // Show/hide filter content based on whether filters are applied
    if (hasFilter) {
        $('#filter-content').show();
        $('#toggle-filter').find('i').removeClass('fa-chevron-down').addClass('fa-chevron-up');
    } else {
        $('#filter-content').hide();
    }


});
</script>
@endpush
