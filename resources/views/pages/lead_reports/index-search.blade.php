@extends('layouts.authenticated')

@section('page.title', __('objects.lead_report_search'))

@section('page.breadcrumb')
    <ol class="breadcrumb">
        <li><a href="{{ route('auth.home') }}"><i class="fa fa-dashboard"></i>{{ __('common.home_breadcrumb') }}</a>
        </li>
        <li class="active">{{ __('objects.lead_report_search') }}</li>
    </ol>
@endsection

@section('page.content')
    <div class="box">
        <div class="box-header with-border">
            <div class="object-list-filter" style="display: flex; align-items: end;">
                @include('pages.lead_reports.partials.index-search-filter-form')
            </div>
        </div>
        @if(!empty($reportsData) && $errors->isEmpty())
            <div class="box-header with-border text-right header-lead-report">
                @if(isset($reportsData['prev_range_time']))
                    <div><PERSON><PERSON> xem cùng kỳ {{ $reportsData['prev_range_time']['start_date_at'] }}
                        đến {{ $reportsData['prev_range_time']['end_date_at'] }}</div>
                @endif
                <div class="btn btn-info btn-view-detail">Xem chi tiết</div>
                <a href="javascript:void(0)" title="In kết kết quả"
                   data-url="{{ route('lead-reports.search.print', request()->all()) }}"
                   class="btn btn-success btn-print">In kết quả</a>
                <a href="{{ route('lead-reports.search.export-excel', request()->all()) }}" title="Xuất excel"
                   class="btn btn-success">Xuất excel</a>
            </div>
        @endif
        <!-- /.box-header -->
        <div class="box-body no-padding">
            @if($errors->any())
                @if(session()->has('lead_report_only_view_last_2_months'))
                    <p class="text-center" style="margin-top: 30px; font-size: 18px; color:red;">
                        {{ session('lead_report_only_view_last_2_months') }}
                    </p>
                @else
                    <p class="text-center" style="margin-top: 30px;">
                        Chưa thể hiển thị dữ liệu khi bạn chưa nhập đủ thông tin cho bộ lọc.
                    </p>
                @endif
            @else
                @include('pages.lead_reports.index-search-table')
            @endif
        </div>
        <!-- /.box-body -->
    </div>
    <!-- /.box -->
@endsection

@section('page.js')
    <script>
        var MARKETING_TEAMS_URL = "{{ route('lead-reports.search.marketing-teams', ['department' => '__departmentId']) }}";
        var MARKETING_TEAMS_BY_BUSINESS_DEPARTMENT_URL = "{{ route('lead-reports.search.marketing-teams-by-business-department') }}";
        var GET_DOCTORS_URL = "{{ route('lead-reports.search.doctors') }}";
        var FAST_FILTER_URL = "{{ route('fast-filter-lead-report.store') }}"
    </script>
    <script src="{{ asset_with_version('js/lead_report/index-search.js') }}"></script>

    <script>
        $(document).on('click', '#js_btn_redirect_to_comparison', function () {
            const url = "{{ route('lead-reports-comparison.index') }}";

            if (!$('#start_date_at').val() || !$('#end_date_at').val() || (!$('#shop_id').val() && !$('#marketing_team_ids').val().length)) {
                alert('Chưa đủ thông tin để chuyển sang màn so sánh.');

                return;
            }

            const params = {
                start_date_at: $('#start_date_at').val(),
                end_date_at: $('#end_date_at').val(),
                shop_id: $('#shop_id').val() ? [$('#shop_id').val()] : [],
            };

            const marketingTeamIds = $('#marketing_team_ids').val();

            if (marketingTeamIds.length) {
                params['marketing_team_id'] = marketingTeamIds;
                params['comparison_type'] = '{{ \App\Enums\LeadReport\ComparisonReportTypeEnum::MarketingTeam }}';
            } else {
                params['comparison_type'] = '{{ \App\Enums\LeadReport\ComparisonReportTypeEnum::Shop }}';
            }

            const query = $.param(params);

            window.location.replace(url + '?' + query);
        });
    </script>
@endsection
