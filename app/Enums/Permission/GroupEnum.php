<?php

declare(strict_types=1);

namespace App\Enums\Permission;

use App\Enums\BaseEnum;

final class GroupEnum extends BaseEnum
{
    public const Booking = 'booking';
    public const BookingSource = 'booking_source';
    public const BookingHistory = 'booking_history';
    public const Customer = 'customer';
    public const CustomerRevenue = 'customer_revenue';
    public const Shop = 'shop';
    public const Team = 'team';
    public const User = 'user';
    public const Role = 'role';
    public const Report = 'report';
    public const Product = 'product';
    public const Department = 'department';
    public const MarketingTeam = 'marketing_team';
    public const MarketingTeamFilter = 'marketing_team_filter';
    public const LeadReport = 'lead_report';
    public const LeadReportDoctor = 'lead_report_doctor';
    public const Doctor = 'doctor';
    public const FastFilterLeadReport = 'fast_filter_lead_report';
    public const StaffDepartment = 'staff_department';
    public const Timesheet = 'timesheet';
    public const CompanyAddress = 'company_address';
    public const Shift = 'shift';
    public const Position = 'position';
    public const Notice = 'notice';
    public const CustomerSurvey = 'customer_survey';
    public const SaleReport = 'sale_report';
    public const ShopKpi = 'shop_kpi';
    public const Shop2Kpi = 'shop_2_kpi';
    public const MarketingTeamKpi = 'marketing_team_kpi';
    public const Kpi = 'kpi';
    public const Rating = 'rating';
    public const UserRating = 'user_rating';
    public const ShiftRuleHistory = 'shift_rule_history';
}
