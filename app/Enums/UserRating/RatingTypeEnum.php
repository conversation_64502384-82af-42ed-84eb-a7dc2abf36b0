<?php

declare(strict_types=1);

namespace App\Enums\UserRating;

use App\Enums\BaseEnum;

final class RatingTypeEnum extends BaseEnum
{
    public const APPOINTMENT = 'appointment_score';
    public const PHONE = 'phone_score';
    public const SERVICE_QUALITY = 'service_quality_score';
    public const INVOICE = 'invoice_score';

    public static function getDescriptions(array $types = []): array
    {
        $values = [
            self::APPOINTMENT => 'Lịch hẹn',
            self::PHONE => 'Số điện thoại',
            self::SERVICE_QUALITY => 'Chất lượng dịch vụ',
            self::INVOICE => 'Đầu hóa đơn',
        ];

        if (empty($types)) {
            return $values;
        }

        return array_intersect_key($values, array_flip($types));
    }

    public static function getSelectOptions(): array
    {
        return [
            self::APPOINTMENT => 'Lịch hẹn',
            self::PHONE => '<PERSON><PERSON> điện thoại', 
            self::SERVICE_QUALITY => 'Chất lượng dịch vụ',
            self::INVOICE => 'Đ<PERSON>u hóa đơn',
        ];
    }
}
