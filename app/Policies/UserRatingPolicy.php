<?php

namespace App\Policies;

use App\Models\User;
use App\Models\UserRating;
use Illuminate\Auth\Access\HandlesAuthorization;

class UserRatingPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('viewAny user_rating');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, UserRating $userRating): bool
    {
        return $user->can('view user_rating');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create user_rating');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, UserRating $userRating): bool
    {
        return $user->can('update user_rating');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, UserRating $userRating): bool
    {
        return $user->can('delete user_rating');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, UserRating $userRating): bool
    {
        return $user->can('restore user_rating');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, UserRating $userRating): bool
    {
        return $user->can('forceDelete user_rating');
    }

    /**
     * Determine whether the user can view reports.
     */
    public function viewReports(User $user): bool
    {
        return $user->can('viewAny user_rating_report');
    }

    /**
     * Determine whether the user can export reports.
     */
    public function exportReports(User $user): bool
    {
        return $user->can('export user_rating_report');
    }
}
