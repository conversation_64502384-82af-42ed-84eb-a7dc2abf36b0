<?php

namespace App\Http\Requests\UserRating;

use App\Enums\MarketingTeam\TeamTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreUpdateUserRatingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('create', \App\Models\UserRating::class) || 
               $this->user()->can('update', \App\Models\UserRating::class);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $userRatingId = $this->route('user_rating') ? $this->route('user_rating')->id : null;

        return [
            'user_id' => [
                'required',
                'exists:users,id',
                Rule::unique('user_ratings', 'user_id')
                    ->where('rating_date', $this->rating_date)
                    ->ignore($userRatingId)
                    ->whereNull('deleted_at')
            ],
            'shop_id' => 'nullable|exists:shops,id',
            'staff_department_id' => 'nullable|exists:staff_departments,id',
            'team_id' => 'nullable|exists:teams,id',
            'team_type' => [
                'nullable',
                'integer',
                Rule::in([TeamTypeEnum::Traditional, TeamTypeEnum::Tiktok])
            ],
            'rating_date' => 'required|date|before_or_equal:today',
            'appointment_score' => 'nullable|numeric|min:0|max:10',
            'phone_score' => 'nullable|numeric|min:0|max:10',
            'service_quality_score' => 'nullable|numeric|min:0|max:10',
            'invoice_score' => 'nullable|numeric|min:0|max:10',
            'notes' => 'nullable|string|max:1000',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'user_id' => 'Nhân viên',
            'shop_id' => 'Chi nhánh',
            'staff_department_id' => 'Phòng ban',
            'team_id' => 'Team',
            'team_type' => 'Loại team',
            'rating_date' => 'Ngày đánh giá',
            'appointment_score' => 'Điểm lịch hẹn',
            'phone_score' => 'Điểm số điện thoại',
            'service_quality_score' => 'Điểm chất lượng dịch vụ',
            'invoice_score' => 'Điểm đầu hóa đơn',
            'notes' => 'Ghi chú',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'user_id.unique' => 'Nhân viên này đã có đánh giá cho ngày đã chọn.',
            'rating_date.before_or_equal' => 'Ngày đánh giá không được là ngày tương lai.',
            '*.min' => ':attribute phải từ 0 trở lên.',
            '*.max' => ':attribute không được vượt quá :max.',
            '*.numeric' => ':attribute phải là số.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Chuyển đổi empty string thành null cho các trường nullable
        $this->merge([
            'shop_id' => $this->shop_id ?: null,
            'staff_department_id' => $this->staff_department_id ?: null,
            'team_id' => $this->team_id ?: null,
            'team_type' => $this->team_type ?: null,
            'appointment_score' => $this->appointment_score ?: null,
            'phone_score' => $this->phone_score ?: null,
            'service_quality_score' => $this->service_quality_score ?: null,
            'invoice_score' => $this->invoice_score ?: null,
            'notes' => $this->notes ?: null,
        ]);
    }
}
