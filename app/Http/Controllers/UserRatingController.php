<?php

namespace App\Http\Controllers;

use App\Models\UserRating;
use App\Models\User;
use App\Models\Shop;
use App\Models\StaffDepartment;
use App\Models\Team;
use App\Http\Requests\UserRating\StoreUpdateUserRatingRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class UserRatingController extends Controller
{
    public function index(Request $request)
    {
        $this->authorize('viewAny', UserRating::class);

        $query = UserRating::query()
            ->with(['user', 'shop', 'staffDepartment', 'team', 'creator']);

        // Filter by user
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by shop
        if ($request->filled('shop_id')) {
            $query->where('shop_id', $request->shop_id);
        }

        // Filter by staff department
        if ($request->filled('staff_department_id')) {
            $query->where('staff_department_id', $request->staff_department_id);
        }

        // Filter by team
        if ($request->filled('team_id')) {
            $query->where('team_id', $request->team_id);
        }

        // Filter by team type
        if ($request->filled('team_type')) {
            $query->where('team_type', $request->team_type);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('rating_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('rating_date', '<=', $request->date_to);
        }

        // Search by user name
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('account', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%");
            });
        }

        // Sort by
        $sortField = $request->input('sort_by', 'rating_date');
        $sortDirection = $request->input('sort_direction', 'desc');

        $allowedSortFields = [
            'id', 'rating_date', 'total_score', 'appointment_score', 
            'phone_score', 'service_quality_score', 'invoice_score', 'created_at'
        ];
        
        if (!in_array($sortField, $allowedSortFields)) {
            $sortField = 'rating_date';
        }

        $query->orderBy($sortField, $sortDirection);

        $userRatingsPagination = $query->paginate(config('common.pagination.per_page.default'))
            ->appends($request->except('page'));

        return view('pages.user-ratings.index', [
            'userRatings' => $userRatingsPagination,
            'users' => User::where('active_flag', true)->orderBy('name')->get(),
            'shops' => Shop::orderBy('name')->get(),
            'staffDepartments' => StaffDepartment::orderBy('name')->get(),
            'teams' => Team::orderBy('name')->get(),
            'teamTypeOptions' => UserRating::getTeamTypeOptions(),
            'request' => $request,
        ]);
    }

    public function create()
    {
        $this->authorize('create', UserRating::class);

        return view('pages.user-ratings.create-edit', [
            'isEdit' => false,
            'users' => User::where('active_flag', true)->orderBy('name')->get(),
            'shops' => Shop::orderBy('name')->get(),
            'staffDepartments' => StaffDepartment::orderBy('name')->get(),
            'teams' => Team::orderBy('name')->get(),
            'teamTypeOptions' => UserRating::getTeamTypeOptions(),
            'ratingTypeOptions' => UserRating::getRatingTypeOptions(),
        ]);
    }

    public function store(StoreUpdateUserRatingRequest $request)
    {
        $this->authorize('create', UserRating::class);

        $data = $request->validated();
        $data['created_by'] = auth()->id();

        // Lấy thông tin user để tự động điền các trường liên quan
        $user = User::with(['team.shop', 'staffDepartment'])->find($data['user_id']);
        
        if ($user) {
            $data['shop_id'] = $data['shop_id'] ?? $user->team?->shop_id;
            $data['staff_department_id'] = $data['staff_department_id'] ?? $user->staff_department_id;
            $data['team_id'] = $data['team_id'] ?? $user->team_id;
            
            // Lấy team_type từ marketing teams nếu user có
            if (!$data['team_type'] && $user->marketingTeams()->exists()) {
                $data['team_type'] = $user->marketingTeams()->first()->team_type;
            }
        }

        UserRating::create($data);

        $this->notifySessionSuccess(
            __('messages.create_object_successfully', ['obj' => 'Đánh giá nhân viên'])
        );

        return redirect()->route('user-ratings.index');
    }

    public function edit(UserRating $userRating)
    {
        $this->authorize('update', $userRating);

        return view('pages.user-ratings.create-edit', [
            'isEdit' => true,
            'userRating' => $userRating,
            'users' => User::where('active_flag', true)->orderBy('name')->get(),
            'shops' => Shop::orderBy('name')->get(),
            'staffDepartments' => StaffDepartment::orderBy('name')->get(),
            'teams' => Team::orderBy('name')->get(),
            'teamTypeOptions' => UserRating::getTeamTypeOptions(),
            'ratingTypeOptions' => UserRating::getRatingTypeOptions(),
        ]);
    }

    public function update(StoreUpdateUserRatingRequest $request, UserRating $userRating)
    {
        $this->authorize('update', $userRating);

        $data = $request->validated();
        $data['updated_by'] = auth()->id();

        $userRating->update($data);

        $this->notifySessionSuccess(
            __('messages.edit_object_successfully', ['obj' => 'Đánh giá nhân viên'])
        );

        return redirect()->route('user-ratings.index');
    }

    public function destroy(UserRating $userRating)
    {
        $this->authorize('delete', $userRating);

        $userRating->delete();

        $this->notifySessionSuccess(
            __('messages.delete_object_successfully', ['obj' => 'Đánh giá nhân viên'])
        );

        return redirect()->back();
    }

    /**
     * Get user info for auto-filling form fields
     */
    public function getUserInfo(User $user)
    {
        $user->load(['team.shop', 'staffDepartment', 'marketingTeams']);
        
        $teamType = null;
        if ($user->marketingTeams()->exists()) {
            $teamType = $user->marketingTeams()->first()->team_type;
        }

        return response()->json([
            'shop_id' => $user->team?->shop_id,
            'staff_department_id' => $user->staff_department_id,
            'team_id' => $user->team_id,
            'team_type' => $teamType,
        ]);
    }
}
