<?php

namespace App\Models;

use App\Enums\UserRating\RatingTypeEnum;
use App\Enums\MarketingTeam\TeamTypeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserRating extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'shop_id',
        'staff_department_id',
        'team_id',
        'team_type',
        'rating_date',
        'appointment_score',
        'phone_score',
        'service_quality_score',
        'invoice_score',
        'total_score',
        'notes',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'rating_date' => 'date',
        'appointment_score' => 'decimal:2',
        'phone_score' => 'decimal:2',
        'service_quality_score' => 'decimal:2',
        'invoice_score' => 'decimal:2',
        'total_score' => 'decimal:2',
        'team_type' => 'integer',
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function shop()
    {
        return $this->belongsTo(Shop::class);
    }

    public function staffDepartment()
    {
        return $this->belongsTo(StaffDepartment::class);
    }

    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Accessors & Mutators
    public function getTeamTypeNameAttribute()
    {
        return match($this->team_type) {
            TeamTypeEnum::Traditional => 'Traditional',
            TeamTypeEnum::Tiktok => 'Tiktok',
            default => 'Unknown'
        };
    }

    // Methods
    public function calculateTotalScore(): float
    {
        $scores = [
            $this->appointment_score,
            $this->phone_score,
            $this->service_quality_score,
            $this->invoice_score
        ];

        $validScores = array_filter($scores, fn($score) => !is_null($score));
        
        return count($validScores) > 0 ? array_sum($validScores) : 0;
    }

    public function updateTotalScore(): void
    {
        $this->total_score = $this->calculateTotalScore();
        $this->save();
    }

    // Static methods
    public static function getRatingTypeOptions(): array
    {
        return RatingTypeEnum::getSelectOptions();
    }

    public static function getTeamTypeOptions(): array
    {
        return [
            TeamTypeEnum::Traditional => 'Traditional',
            TeamTypeEnum::Tiktok => 'Tiktok',
        ];
    }

    // Scopes
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('rating_date', [$startDate, $endDate]);
    }

    public function scopeByShop($query, $shopId)
    {
        return $query->where('shop_id', $shopId);
    }

    public function scopeByStaffDepartment($query, $staffDepartmentId)
    {
        return $query->where('staff_department_id', $staffDepartmentId);
    }

    public function scopeByTeam($query, $teamId)
    {
        return $query->where('team_id', $teamId);
    }

    public function scopeByTeamType($query, $teamType)
    {
        return $query->where('team_type', $teamType);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    // Boot method để tự động tính total_score
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($model) {
            $model->total_score = $model->calculateTotalScore();
        });
    }
}
