<?php

namespace App\Models;

use App\Contracts\Repositories\ShiftRepositoryInterface;
use App\Models\Chat\Participation;
use App\Models\Traits\HasRoles;
use App\Services\Auth\Traits\HasCheckingTooAttempts;
use App\Services\Firebase\FirebaseService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Laravel\Passport\HasApiTokens;

class User extends Authenticatable
{
    use HasFactory;
    use SoftDeletes;
    use HasApiTokens;
    use Notifiable;
    use HasRoles;
    use HasCheckingTooAttempts;

    protected $fillable = [
        'team_id',
        'name',
        'account',
        'password',
        'phone_number',
        'user_avatar_id',
        'staff_department_chief_flag',
        'staff_department_id',
        'code',
        'active_flag',
        'default_shift_id',
        'device_code',
        'business_department_id',
    ];

    protected $casts = [
        'staff_department_chief_flag' => 'boolean',
        'active_flag' => 'boolean',
    ];

    protected $hidden = [
        'password',
    ];

    public function departments()
    {
        return $this->belongsToMany(Department::class, UserDepartment::class);
    }

    public function department()
    {
        return $this->hasOneThrough(Department::class, UserDepartment::class, 'user_id', 'id', 'id', 'department_id');
    }

    public function devices()
    {
        return $this->hasMany(UserDevice::class);
    }

    public function managers()
    {
        return $this->belongsToMany(User::class, UserManager::class, 'user_id', 'manager_user_id')
            ->withTimestamps()
            ->where('active_flag', true)
            ->whereNull('user_managers.deleted_at'); // Filter theo soft delete của pivot table
    }

    public function originalStaffs()
    {
        return $this->belongsToMany(User::class, UserManager::class, 'manager_user_id', 'user_id')
            ->withTimestamps()
            ->whereNull('user_managers.deleted_at'); // Filter theo soft delete của pivot table
    }

    public function staffs()
    {
        return $this->originalStaffs()
            ->where('active_flag', true);
    }

    public function shops()
    {
        return $this->belongsToMany(Shop::class, UserShop::class);
    }

    public function doctors()
    {
        return $this->belongsToMany(Doctor::class, UserDoctor::class);
    }

    public function positions()
    {
        return $this->belongsToMany(Position::class, UserPosition::class);
    }

    public function marketingTeams()
    {
        return $this->belongsToMany(MarketingTeam::class, UserMarketingTeam::class);
    }

    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    public function shifts()
    {
        return $this->belongsToMany(Shift::class, UserShift::class);
    }

    public function defaultShift()
    {
        return $this->belongsTo(Shift::class, 'default_shift_id');
    }

    public function timesheets()
    {
        return $this->hasMany(Timesheet::class);
    }

    public function staffDepartment()
    {
        return $this->belongsTo(StaffDepartment::class, 'staff_department_id');
    }

    public function setting()
    {
        return $this->hasOne(UserSetting::class);
    }

    public function avatar()
    {
        return $this->belongsTo(UserAvatar::class, 'user_avatar_id');
    }

    public function pinnedConversationParticipantions()
    {
        return $this->hasMany(Participation::class, 'participant_id')
            ->where('left_flag', false)
            ->where('pinned_flag', true);
    }

    public function userRatings()
    {
        return $this->hasMany(UserRating::class);
    }

    public function createdUserRatings()
    {
        return $this->hasMany(UserRating::class, 'created_by');
    }

    public function isChiefOfStaffDepartment(): bool
    {
        return $this->staff_department_chief_flag;
    }

    public function isActivate(): bool
    {
        return (bool)$this->active_flag;
    }

    public function isInactivate(): bool
    {
        return !$this->isActivate();
    }

    public function createFirebaseCustomToken(): string
    {
        return app(FirebaseService::class)->createCustomToken($this->getKey(), [
            'view_any_chat_message' => (bool)$this->setting?->view_any_chat_service,
        ]);
    }

    public function routeNotificationForFcm(): array
    {
        return $this->devices()->pluck('device_token')->toArray();
    }

    public function findAndValidateForPassport(string $username, string $password): static
    {
        $this->checkTooManyFailedAttempts(
            $throttleKey = $this->getThrottleKey($username, request()->ip())
        );

        /** @var static $user */
        $user = $this->newQuery()->where('account', $username)->first();

        if ($user && $user->isInactivate()) {
            throw ValidationException::withMessages([
                'account' => __('messages.account_or_password_inactive'),
            ]);
        }

        if (!$user || !Hash::check($password, $user->password)) {
            $this->hitFailedAttempts($throttleKey);
            throw ValidationException::withMessages([
                'account' => __('messages.account_or_password_wrong'),
            ]);
        }

        $this->clearFailedAttempts($throttleKey);

        return $user;
    }

    public function getAvailableShifts()
    {
        $shifts = $this->shifts;

        if ($shifts->count()) {
            return $shifts;
        }

        return app(ShiftRepositoryInterface::class)->all();
    }

    public function businessDepartment()
    {
        return $this->belongsTo(BusinessDepartment::class);
    }
}
