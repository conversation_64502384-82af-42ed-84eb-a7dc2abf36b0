<?php

namespace App\Services\Menu\AdminMenu;

use App\Contracts\Repositories\DepartmentRepositoryInterface;
use App\Contracts\Repositories\MarketingTeamRepositoryInterface;
use App\Enums\Department\DepartmentTypeEnum;
use App\Enums\Permission\GroupEnum;
use App\Models\User;
use App\Services\Menu\AbstractMenuElement;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use App\Services\Menu\AbstractMenuService;
use App\Enums\MarketingTeam\TeamTypeEnum as MarketingTeamTeamTypeEnum;

class AdminMenuService extends AbstractMenuService
{
    protected DepartmentRepositoryInterface $departmentRepository;
    protected MarketingTeamRepositoryInterface $marketingTeamRepository;

    public function __construct(
        DepartmentRepositoryInterface $departmentRepository,
        MarketingTeamRepositoryInterface $marketingTeamRepository
    ) {
        $this->departmentRepository = $departmentRepository;
        $this->marketingTeamRepository = $marketingTeamRepository;
    }
    public function create(User $user): AbstractMenuElement
    {
        $menuItems = [];

        foreach ($this->configs($user) as $config) {
            if (empty($config['group_items'])) {
                $menuItems[] = $this->createMenuItemOrMenuItemWithSubItems($config);
            } else {
                $groupItems = [];

                foreach ($config['group_items'] as $item) {
                    $groupItems[] = $this->createMenuItemOrMenuItemWithSubItems($item);
                }

                $menuItems[] = $this->createMenuGroup($config, $groupItems);
            }
        }

        return $this->createMenu($menuItems);
    }

    protected function configs(User $user): array
    {
        return [
            [
                'name' => __('objects.dashboard'),
                'route' => [
                    'name' => 'dashboard',
                ],
                'icon' => 'fa fa-dashboard',
                'display' => true,
            ],
            [
                'name' => __('objects.report_revenue'),
                'route' => [
                    'name' => 'reports.revenue',
                ],
                'icon' => 'fa fa-dollar',
                'display' => $user->can(permission_name('viewRevenue', \App\Enums\Permission\GroupEnum::Report)),
            ],
            [
                'name' => 'Báo cáo LEAD',
                'route' => [
                    'name' => 'lead-reports.search.index',
                    'params' => [
                        'report_type' => 'department',
                        'start_date_at' => \Carbon\Carbon::now()->subDay()->firstOfMonth()->format(config('common.datetime.format.client.date')),
                        'end_date_at' => \Carbon\Carbon::now()->format(config('common.datetime.format.client.date')),
                        'show_avg_day_by_day' => true,
                        'department_ids' => $this->getSafeDepartmentIds(),
                        'marketing_team_ids' => $this->getAllMarketingTeamIds(),
                        'type_name' => 'lead_all',
                    ],
                ],
                'icon' => 'fa fa-file',
                'display' => false,
            ],
            [
                'name' => 'Báo cáo LEAD',
                'icon' => 'fa fa-file',
                'sub_items' => [
                    [
                        'name' => 'Xem tất cả',
                        'route' => [
                            'name' => 'lead-reports.search.index',
                            'params' => [
                                'report_type' => 'department',
                                'start_date_at' => \Carbon\Carbon::now()->subDay()->firstOfMonth()->format(config('common.datetime.format.client.date')),
                                'end_date_at' => \Carbon\Carbon::now()->format(config('common.datetime.format.client.date')),
                                'show_avg_day_by_day' => true,
                                'department_ids' => $this->getSafeDepartmentIds(),
                                'marketing_team_ids' => $this->getAllMarketingTeamIds(),
                                'type_name' => 'lead_all',
                            ],
                        ],
                        'type_name' => 'lead_all',
                        'icon' => 'fa fa-list-alt',
                        'display' => $user->can('viewReportAll', \App\Models\LeadReport::class),
                    ],
                    [
                        'name' => 'BC Lead SEEDING',
                        'icon' => 'fa fa-users',
                        'display' => $user->can('viewReportDepartment', \App\Models\LeadReport::class),
                        'sub_items' => [
                            [
                                'name' => 'Xem BC Lead Seeding',
                                'route' => [
                                    'name' => 'lead-reports.search.index',
                                    'params' => array_merge(
                                        $this->getDepartmentParams($this->getDepartmentsByType(DepartmentTypeEnum::Seeding, MarketingTeamTeamTypeEnum::Traditional)),
                                        [
                                            'team_type' => MarketingTeamTeamTypeEnum::Traditional,
                                            'department_type' => DepartmentTypeEnum::Seeding,
                                            'type_name' => 'lead_seeding'
                                        ]
                                    ),
                                ],
                                'type_name' => 'lead_seeding',
                                'department_type' => DepartmentTypeEnum::Seeding,
                                'icon' => 'fa fa-circle-o',
                                'display' => $user->can('viewReportDepartment', \App\Models\LeadReport::class),
                            ],
                            [
                                'name' => 'Xem BC Lead Seeding Tiktok',
                                'route' => [
                                    'name' => 'lead-reports.search.index',
                                    'params' => array_merge(
                                        $this->getDepartmentParams($this->getDepartmentsByType(DepartmentTypeEnum::Seeding, MarketingTeamTeamTypeEnum::Tiktok)),
                                        [
                                            'team_type' => MarketingTeamTeamTypeEnum::Tiktok,
                                            'department_type' => DepartmentTypeEnum::Seeding,
                                            'type_name' => 'lead_seeding_tiktok'
                                        ]
                                    ),
                                ],
                                'type_name' => 'lead_seeding_tiktok',
                                'department_type' => DepartmentTypeEnum::Seeding,
                                'icon' => 'fa fa-circle-o',
                                'display' => $user->can('viewReportDepartment', \App\Models\LeadReport::class),
                            ],
                        ],
                    ],
                    [
                        'name' => 'BC Lead ADS',
                        'icon' => 'fa fa-bullhorn',
                        'display' => $user->can('viewReportDepartment', \App\Models\LeadReport::class),
                        'sub_items' => [
                            [
                                'name' => 'Xem BC Lead ADS',
                                'route' => [
                                    'name' => 'lead-reports.search.index',
                                    'params' => array_merge(
                                        $this->getDepartmentParams($this->getDepartmentsByType(DepartmentTypeEnum::Ads, MarketingTeamTeamTypeEnum::Traditional)),
                                        [
                                            'team_type' => MarketingTeamTeamTypeEnum::Traditional,
                                            'department_type' => DepartmentTypeEnum::Ads,
                                            'type_name' => 'lead_ads'
                                        ]
                                    ),
                                ],
                                'type_name' => 'lead_ads',
                                'department_type' => DepartmentTypeEnum::Ads,
                                'icon' => 'fa fa-circle-o',
                                'display' => $user->can('viewReportDepartment', \App\Models\LeadReport::class),
                            ],
                            [
                                'name' => 'Xem BC Lead ADS Tiktok',
                                'route' => [
                                    'name' => 'lead-reports.search.index',
                                    'params' => array_merge(
                                        $this->getDepartmentParams($this->getDepartmentsByType(DepartmentTypeEnum::Ads, MarketingTeamTeamTypeEnum::Tiktok)),
                                        [
                                            'team_type' => MarketingTeamTeamTypeEnum::Tiktok,
                                            'department_type' => DepartmentTypeEnum::Ads,
                                            'type_name' => 'lead_ads_tiktok'
                                        ]
                                    ),
                                ],
                                'type_name' => 'lead_ads_tiktok',
                                'department_type' => DepartmentTypeEnum::Ads,
                                'icon' => 'fa fa-circle-o',
                                'display' => $user->can('viewReportDepartment', \App\Models\LeadReport::class),
                            ],
                        ],
                    ],
                    // Kiểm tra xem Department với ID 4 có tồn tại không
                    ($this->departmentRepository->find(4) !== null) ? [
                        'name' => 'Xem BC Lead Bác sĩ',
                        'route' => [
                            'name' => 'lead-reports.search.index',
                            'params' => array_merge(
                                $this->getDoctorParams(4),
                                [
                                    'department_ids' => $this->getDepartmentsByType(DepartmentTypeEnum::Seeding, null),
                                    'type_name' => 'lead_doctor'
                                ]
                            ),
                        ],
                        'type_name' => 'lead_doctor',
                        'icon' => 'fa fa-user-md',
                        'display' => $user->can('viewReportDoctorAll', \App\Models\LeadReport::class),
                    ] : null,
                    [
                        'name' => 'Xem BC Lead dịch vụ',
                        'route' => [
                            'name' => 'rating-reports.index',
                        ],
                        'icon' => 'fa fa-bar-chart',
                        'display' => $user->can('viewReportDoctorAll', \App\Models\LeadReport::class) || $user->can('viewReportService', \App\Models\LeadReport::class),
                        'active' => [
                            'starts_with_route' => 'rating-reports',
                        ],
                    ],
                    [
                        'name' => 'Nhập BC LEAD',
                        'route' => [
                            'name' => 'lead-reports.index',
                        ],
                        'icon' => 'fa fa-pencil-square-o',
                        'display' => $user->can('viewAny', \App\Models\LeadReport::class),
                        'active' => [
                            'starts_with_route' => 'lead-reports.',
                        ],
                    ],
                    // [
                    //     'name' => 'Nhập BC LEAD bác sĩ',
                    //     'route' => [
                    //         'name' => 'lead-report-doctors.index',
                    //     ],
                    //     'icon' => 'fa fa-circle-o',
                    //     'display' => $user->can('viewAny', \App\Models\LeadReportDoctor::class),
                    //     'active' => [
                    //         'starts_with_route' => 'lead-report-doctors.',
                    //     ],
                    // ],
                    [
                        'name' => 'Xem so sánh BC LEAD',
                        'route' => [
                            'name' => 'lead-reports-comparison.index',
                        ],
                        'icon' => 'fa fa-bar-chart',
                        'display' => $user->can('viewComparisonReport', \App\Models\LeadReport::class),
                        'active' => [
                            'starts_with_route' => 'lead-reports-comparison.',
                        ],
                    ],
                ],
            ],
            [
                'name' => 'Báo cáo SALE',
                'icon' => 'fa fa-file',
                'sub_items' => [
                    [
                        'name' => 'Xem BC SALE',
                        'route' => [
                            'name' => 'sale-reports-statistic.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewReport', \App\Models\SaleReport::class),
                    ],
                    [
                        'name' => 'Nhập BC SALE',
                        'route' => [
                            'name' => 'sale-reports.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\SaleReport::class),
                        'active' => [
                            'starts_with_route' => 'sale-reports.',
                        ],
                    ],
                ],
            ],
            [
                'name' => __('objects.booking'),
                'icon' => 'fa fa-clock-o',
                'sub_items' => [
                    [
                        'name' => 'Hôm nay (theo tháng)',
                        'route' => [
                            'name' => 'bookings.index.date',
                            'params' => [
                                'type' => \App\Enums\Booking\BookingTypeEnum::New,
                                'type_name' => 'booking_today'
                            ],
                        ],
                        'type_name' => 'booking_today',
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\Booking::class),
                        'active' => [
                            'starts_with_route' => 'bookings',
                        ],
                    ],
                    [
                        'name' => 'Tất cả',
                        'route' => [
                            'name' => 'bookings.index',
                            'params' => [
                                'type_name' => 'booking_all'
                            ],
                        ],
                        'type_name' => 'booking_all',
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\Booking::class),
                        'active' => [
                            'starts_with_route' => 'bookings',
                        ],
                    ],
                ],
            ],
            [
                'name' => __('objects.customer'),
                'icon' => 'fa fa-user',
                'sub_items' => [
                    [
                        'name' => 'Tất cả',
                        'route' => [
                            'name' => 'customers.index',
                            'params' => [
                                'type_name' => 'customer_all'
                            ],
                        ],
                        'type_name' => 'customer_all',
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\Customer::class),
                        'active' => [
                            'starts_with_route' => 'customers',
                        ],
                    ],
                    [
                        'name' => 'Khách mới',
                        'route' => [
                            'name' => 'customers.index',
                            'params' => ['type' => \App\Enums\Customer\CustomerTypeEnum::NewCustomer],
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\Customer::class),
                        'active' => [
                            'starts_with_route' => 'customers',
                        ],
                    ],
                    [
                        'name' => 'Khách cũ',
                        'route' => [
                            'name' => 'customers.index',
                            'params' => ['type' => \App\Enums\Customer\CustomerTypeEnum::OldCustomer],
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\Customer::class),
                        'active' => [
                            'starts_with_route' => 'customers',
                        ],
                    ],
                    [
                        'name' => __('objects.customer_survey'),
                        'route' => [
                            'name' => 'customer-surveys.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\CustomerSurvey::class),
                        'active' => [
                            'starts_with_route' => 'customer-surveys',
                        ],
                    ],
                ],
            ],
            [
                'name' => __('objects.customer_revenue'),
                'icon' => 'fa fa-dollar',
                'sub_items' => [
                    [
                        'name' => 'Hôm nay (theo tháng)',
                        'route' => [
                            'name' => 'customer-revenues.index.date',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\CustomerRevenue::class),
                        'active' => [
                            'starts_with_route' => 'customer-revenues',
                        ],
                    ],
                    [
                        'name' => 'Tất cả',
                        'route' => [
                            'name' => 'customer-revenues.index',
                            'params' => [
                                'type_name' => 'customer_revenue_all'
                            ],
                        ],
                        'type_name' => 'customer_revenue_all',
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\CustomerRevenue::class),
                        'active' => [
                            'starts_with_route' => 'customer-revenues',
                        ],
                    ],
                ],
            ],
            [
                'name' => __('objects.revenue_head_evaluation'),
                'route' => [
                    'name' => 'revenue-head-evaluations.index',
                ],
                'icon' => 'fa fa-star',
                'display' => $user->can('viewRevenueHeadEvaluation', \App\Models\Booking::class),
                'active' => [
                    'starts_with_route' => 'revenue-head-evaluations',
                ],
            ],
            [
                'name' => __('objects.rating'),
                'icon' => 'fa fa-star',
                'sub_items' => [
                    [
                        'name' => 'Tất cả',
                        'route' => [
                            'name' => 'ratings.index',
                            'params' => [
                                'type_name' => 'rating_all'
                            ],
                        ],
                        'type_name' => 'rating_all',
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\Rating::class),
                        'active' => [
                            'starts_with_route' => 'ratings',
                        ],
                    ],

                ],
            ],
            [
                'name' => 'Nhân viên',
                'icon' => 'fa fa-users',
                'sub_items' => [
                    [
                        'name' => __('objects.user'),
                        'route' => [
                            'name' => 'users.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\User::class),
                        'active' => [
                            'starts_with_route' => 'users',
                        ],
                    ],

                    [
                        'name' => 'Người quản lý của tôi',
                        'route' => [
                            'name' => 'my-manager.index',
                        ],
                        'icon' => 'fa fa-user-plus',
                        'display' => true, // Tất cả user đều có thể chọn manager
                        'active' => [
                            'starts_with_route' => 'my-manager',
                        ],
                    ],
                    [
                        'name' => __('objects.role'),
                        'route' => [
                            'name' => 'roles.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\Role::class),
                        'active' => [
                            'starts_with_route' => 'roles',
                        ],
                    ],
                ],
            ],
            [
                'name' => 'Đánh giá xếp hạng',
                'icon' => 'fa fa-star',
                'sub_items' => [
                    [
                        'name' => 'Đánh giá nhân viên',
                        'route' => [
                            'name' => 'user-ratings.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\UserRating::class),
                        'active' => [
                            'starts_with_route' => 'user-ratings',
                        ],
                    ],
                    [
                        'name' => 'Báo cáo xếp hạng',
                        'route' => [
                            'name' => 'user-rating-reports.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewReports', \App\Models\UserRating::class),
                        'active' => [
                            'starts_with_route' => 'user-rating-reports',
                        ],
                    ],
                    [
                        'name' => 'Đánh giá dịch vụ',
                        'route' => [
                            'name' => 'ratings.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\Rating::class),
                        'active' => [
                            'starts_with_route' => 'ratings',
                        ],
                    ],
                    [
                        'name' => 'Báo cáo đánh giá dịch vụ',
                        'route' => [
                            'name' => 'rating-reports.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\Rating::class),
                        'active' => [
                            'starts_with_route' => 'rating-reports',
                        ],
                    ],
                ],
            ],
            [
                'name' => 'Chấm công',
                'icon' => 'fa fa-calendar-check-o',
                'sub_items' => [
                    [
                        'name' => __('objects.timesheet'),
                        'route' => [
                            'name' => 'timesheets.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\Timesheet::class),
                        'active' => [
                            'starts_with_route' => 'timesheets',
                        ],
                    ],
                    [
                        'name' => __('objects.shift'),
                        'route' => [
                            'name' => 'shifts.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\Shift::class),
                        'active' => [
                            'starts_with_route' => 'shifts',
                        ],
                    ],
                    [
                        'name' => 'Giải trình chấm công',
                        'route' => [
                            'name' => 'attendance-explanation.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => true, // Tất cả user đều có thể giải trình chấm công
                        'active' => [
                            'starts_with_route' => 'attendance-explanation.index',
                        ],
                    ],
                    [
                        'name' => 'Xác nhận giải trình',
                        'route' => [
                            'name' => 'attendance-explanation.tagged-confirmations',
                        ],
                        'icon' => 'fa fa-user-check',
                        'display' => true, // Tất cả user đều có thể được tag để xác nhận
                        'active' => [
                            'starts_with_route' => 'attendance-explanation.tagged-confirmations',
                        ],
                    ],
                    [
                        'name' => 'Duyệt giải trình (Manager)',
                        'route' => [
                            'name' => 'attendance-explanation.manager-approval',
                        ],
                        'icon' => 'fa fa-check-circle',
                        'display' => $this->userHasManagerRole($user), // Chỉ manager mới thấy
                        'active' => [
                            'starts_with_route' => 'attendance-explanation.manager-approval',
                        ],
                    ],
                    [
                        'name' => 'Duyệt giải trình (HR)',
                        'route' => [
                            'name' => 'attendance-explanation.hr-approval',
                        ],
                        'icon' => 'fa fa-building',
                        'display' => $user->hasRole('R080'), // Chỉ role R080 mới thấy
                        'active' => [
                            'starts_with_route' => 'attendance-explanation.hr-approval',
                        ],
                    ],
                    [
                        'name' => __('objects.shift_rule_history'),
                        'route' => [
                            'name' => 'shift-rule-histories.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\ShiftRuleHistory::class),
                        'active' => [
                            'starts_with_route' => 'shift-rule-histories',
                        ],
                    ],
                ],
            ],
            [
                'name' => 'KPI',
                'icon' => 'fa fa-check',
                'sub_items' => [
                    [
                        'name' => __('objects.kpi_comparison_chart'),
                        'route' => [
                            'name' => 'kpi.comparison-chart',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can(permission_name('viewKpiChartShop', GroupEnum::Kpi))
                            || $user->can(permission_name('viewKpiChartMarketingTeam', GroupEnum::Kpi)),
                    ],
                    [
                        'name' => __('objects.shop_kpi'),
                        'route' => [
                            'name' => 'shop-kpis.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\ShopKpi::class),
                        'active' => [
                            'starts_with_route' => 'shop-kpis.',
                        ],
                    ],
                    [
                        'name' => __('objects.marketing_team_kpi'),
                        'route' => [
                            'name' => 'marketing-team-kpis.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\MarketingTeamKpi::class),
                        'active' => [
                            'starts_with_route' => 'marketing-team-kpis.',
                        ],
                    ],
                    [
                        'name' => __('objects.shop_2_kpi'),
                        'route' => [
                            'name' => 'shop-2-kpis.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('manage', \App\Models\Shop2Kpi::class),
                        'active' => [
                            'starts_with_route' => 'shop-2-kpis.',
                        ],
                    ],
                ],
            ],
            [
                'name' => 'Quản lý khác',
                'icon' => 'fa fa-tasks',
                'sub_items' => [
                    [
                        'name' => __('objects.position'),
                        'route' => [
                            'name' => 'positions.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\Position::class),
                        'active' => [
                            'starts_with_route' => 'positions',
                        ],
                    ],
                    [
                        'name' => __('objects.department'),
                        'route' => [
                            'name' => 'departments.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\Department::class),
                        'active' => [
                            'starts_with_route' => 'departments',
                        ],
                    ],
                    [
                        'name' => __('objects.staff_department'),
                        'route' => [
                            'name' => 'staff-departments.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\StaffDepartment::class),
                        'active' => [
                            'starts_with_route' => 'staff-departments',
                        ],
                    ],
                    [
                        'name' => __('objects.product'),
                        'route' => [
                            'name' => 'products.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\Product::class),
                        'active' => [
                            'starts_with_route' => 'products',
                        ],
                    ],
                    [
                        'name' => __('objects.booking_source'),
                        'route' => [
                            'name' => 'booking-sources.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\BookingSource::class),
                        'active' => [
                            'starts_with_route' => 'booking-sources',
                        ],
                    ],
                    [
                        'name' => __('objects.company_address'),
                        'route' => [
                            'name' => 'company-addresses.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\CompanyAddress::class),
                        'active' => [
                            'starts_with_route' => 'company-addresses',
                        ],
                    ],
                    [
                        'name' => __('objects.shop'),
                        'route' => [
                            'name' => 'shops.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\Shop::class),
                        'active' => [
                            'starts_with_route' => 'shops',
                        ],
                    ],
                    [
                        'name' => __('objects.doctor'),
                        'route' => [
                            'name' => 'doctors.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\Doctor::class),
                        'active' => [
                            'starts_with_route' => 'doctors',
                        ],
                    ],
                    [
                        'name' => __('objects.team'),
                        'route' => [
                            'name' => 'teams.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\Team::class),
                        'active' => [
                            'starts_with_route' => 'teams',
                        ],
                    ],
                    [
                        'name' => __('objects.marketing_team'),
                        'route' => [
                            'name' => 'marketing-teams.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\MarketingTeam::class),
                        'active' => [
                            'starts_with_route' => 'marketing-teams',
                        ],
                    ],
                    [
                        'name' => __('objects.marketing_team_filter'),
                        'route' => [
                            'name' => 'marketing-team-filters.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\MarketingTeamFilter::class),
                        'active' => [
                            'starts_with_route' => 'marketing-team-filters',
                        ],
                    ],
                    [
                        'name' => __('objects.conversation'),
                        'route' => [
                            'name' => 'conversations.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAnyWeb', \App\Models\Chat\Conversation::class),
                        'active' => [
                            'starts_with_route' => 'conversations',
                        ],
                    ],
                    [
                        'name' => __('objects.user_action_log'),
                        'route' => [
                            'name' => 'user-action-logs.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewAny', \App\Models\UserActionLog::class),
                        'active' => [
                            'starts_with_route' => 'user-action-logs',
                        ],
                    ],
                    [
                        'name' => __('objects.notice'),
                        'route' => [
                            'name' => 'notices.index',
                        ],
                        'icon' => 'fa fa-bell-o',
                        'display' => $user->can('viewAny', \App\Models\Notice::class),
                        'active' => [
                            'starts_with_route' => 'notices',
                        ],
                    ],
                ],
            ],
            [
                'name' => 'Quản lý hệ thống',
                'icon' => 'fa fa-cog',
                'sub_items' => [
                    [
                        'name' => 'Logs',
                        'route' => [
                            'name' => 'common.logs',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->isSuperAdministrator(),
                    ],
                    [
                        'name' => 'Queues',
                        'route' => [
                            'name' => 'horizon.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->can('viewHorizon'),
                    ],
                    [
                        'name' => __('objects.urgent_feature'),
                        'route' => [
                            'name' => 'urgent-features.index',
                        ],
                        'icon' => 'fa fa-circle-o',
                        'display' => $user->isSuperAdministrator(),
                    ],
                ],
            ],
        ];
    }

    /**
     * Kiểm tra user có phải manager không (có nhân viên dưới quyền)
     */
    private function userHasManagerRole(User $user): bool
    {
        return DB::table('user_managers')
            ->where('manager_user_id', $user->id)
            ->whereNull('deleted_at')
            ->exists();
    }



    protected function createMenuItemOrMenuItemWithSubItems(array $config): AdminMenuItem|AdminMenuItemWithSubItems
    {
        if (empty($config['sub_items'])) {
            return $this->createMenuItem($config);
        }

        $subItems = [];

        foreach ($config['sub_items'] as $childConfig) {
            $subItems[] = $this->createMenuItemOrMenuItemWithSubItems($childConfig);
        }

        return $this->createMenuItemWithSubItems($config, $subItems);
    }

    protected function createMenu(array $menuItems): AdminMenu
    {
        $menu = new AdminMenu();

        $this->addElementsToMenuMultipleElements($menu, $menuItems);

        return $menu;
    }

    protected function createMenuGroup(array $config, array $groupItems): AdminMenuGroupItems
    {
        $menuGroup = new AdminMenuGroupItems($config);

        $this->addElementsToMenuMultipleElements($menuGroup, $groupItems);

        return $menuGroup;
    }

    protected function createMenuItemWithSubItems(array $config, array $subItems): AdminMenuItemWithSubItems
    {
        $menuItemWithSubItems = new AdminMenuItemWithSubItems($config);

        $this->addElementsToMenuMultipleElements($menuItemWithSubItems, $subItems);

        return $menuItemWithSubItems;
    }

    protected function createMenuItem(array $config): AdminMenuItem
    {
        return new AdminMenuItem(array_merge($config, [
            'url' => $this->detachUrl($config),
            'is_active' => $this->detachIsActiveUrl($config),
        ]));
    }

    protected function getDepartmentParams(array $departmentIds): array
    {
        return [
            'report_type' => 'department',
            'start_date_at' => \Carbon\Carbon::now()->subDay()->firstOfMonth()->format(config('common.datetime.format.client.date')),
            'end_date_at' => \Carbon\Carbon::now()->format(config('common.datetime.format.client.date')),
            'department_ids' => $departmentIds,
            'show_avg_day_by_day' => true,
        ];
    }

    protected function getDepartmentsByType(string $type, ?int $teamType = null): array
    {
        $departmentIds = $this->departmentRepository->getIdsByType($type);

        if ($teamType === null) {
            return $departmentIds;
        }

        $filteredIds = [];
        $departments = $this->departmentRepository->getByIds($departmentIds);

        foreach ($departments as $department) {
            $isTiktok = strpos($department->name, 'TIKTOK') !== false;

            if ($teamType === MarketingTeamTeamTypeEnum::Tiktok && $isTiktok) {
                $filteredIds[] = $department->id;
            }
            elseif ($teamType === MarketingTeamTeamTypeEnum::Traditional && !$isTiktok) {
                $filteredIds[] = $department->id;
            }
        }

        return $filteredIds;
    }

    protected function getDoctorParams($departmentId): array
    {
        // Kiểm tra nếu departmentId là mảng rỗng hoặc null
        if (empty($departmentId) || (is_array($departmentId) && count($departmentId) === 0)) {
            return [
                'report_type' => 'doctor',
                'start_date_at' => \Carbon\Carbon::now()->subDay()->firstOfMonth()->format(config('common.datetime.format.client.date')),
                'end_date_at' => \Carbon\Carbon::now()->format(config('common.datetime.format.client.date')),
                'show_avg_day_by_day' => true,
                'department_id' => 0, // Giá trị mặc định khi không có department
            ];
        }

        $params = [
            'report_type' => 'doctor',
            'start_date_at' => \Carbon\Carbon::now()->subDay()->firstOfMonth()->format(config('common.datetime.format.client.date')),
            'end_date_at' => \Carbon\Carbon::now()->format(config('common.datetime.format.client.date')),
            'department_id' => $departmentId,
            'show_avg_day_by_day' => true,
        ];

        try {
            // Nếu departmentId là mảng, lấy phần tử đầu tiên
            $deptId = is_array($departmentId) ? ($departmentId[0] ?? 0) : $departmentId;

            // Chỉ tìm department nếu deptId > 0
            if ($deptId > 0) {
                $department = $this->departmentRepository->find($deptId);

                if ($department && $department->type === DepartmentTypeEnum::Seeding) {
                    if (strpos($department->name, 'TIKTOK') !== false) {
                        $params['team_type'] = MarketingTeamTeamTypeEnum::Tiktok;
                    } else {
                        $params['team_type'] = MarketingTeamTeamTypeEnum::Traditional;
                    }
                }
            }
        } catch (\Exception $e) {
            // Xử lý ngoại lệ nếu có
            \Log::error('Error in getDoctorParams: ' . $e->getMessage());
        }

        return $params;
    }

    protected function getAllMarketingTeamIds(): array
    {
        return $this->marketingTeamRepository->all()->pluck('id')->toArray();
    }

    protected function getSafeDepartmentIds(): array
    {
        try {
            return $this->departmentRepository->findWhere(['deleted_at' => null])->pluck('id')->toArray();
        } catch (\Exception $e) {
            // Nếu có lỗi, trả về mảng rỗng
            \Log::error('Error in getSafeDepartmentIds: ' . $e->getMessage());
            return [];
        }
    }

    protected function detachUrl(array $config): ?string
    {
        if (isset($config['url'])) {
            return $config['url'];
        }

        if (isset($config['route'])) {
            $url = route($config['route']['name'], $config['route']['params'] ?? []);

            if (isset($config['clear_query_params']) && $config['clear_query_params']) {
                $urlParts = parse_url($url);
                return $urlParts['scheme'] . '://' . $urlParts['host'] . $urlParts['path'];
            }

            return $url;
        }

        return null;
    }
}
